import React, { useState, useEffect } from 'react';
import {
  ZoomIn,
  ZoomOut,
  Download,
  Upload,
  RotateCcw,
  Sun,
  Moon,
  Plus,
  KeyRound
} from 'lucide-react';
import { ViewportState, CanvasData } from '../types';
import { Theme } from '../hooks/useTheme';
import { ConfirmationDialog } from './ConfirmationDialog';
import { ModelSelector } from './ModelSelector';
import { CanvasTabs } from './CanvasTabs';

interface ToolbarProps {
  viewport: ViewportState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  onExport: () => void;
  onImport: () => void;
  onNewCanvas: () => void;
  theme: Theme;
  onToggleTheme: () => void;
  apiKey: string;
  onSetApiKey: (key: string) => void;
  selectedModel: string;
  onSetSelectedModel: (model: string) => void;
  // Canvas management props
  canvases: Record<string, CanvasData>;
  canvasOrder: string[];
  activeCanvasId: string;
  onSwitchCanvas: (canvasId: string) => void;
  onCloseCanvas: (canvasId: string) => void;
  onRenameCanvas: (canvasId: string, newName: string) => void;
  onDuplicateCanvas: (canvasId: string) => void;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  viewport,
  onZoomIn,
  onZoomOut,
  onResetView,
  onExport,
  onImport,
  onNewCanvas,
  theme,
  onToggleTheme,
  apiKey,
  onSetApiKey,
  selectedModel,
  onSetSelectedModel,
  canvases,
  canvasOrder,
  activeCanvasId,
  onSwitchCanvas,
  onCloseCanvas,
  onRenameCanvas,
  onDuplicateCanvas,
}) => {
  const [localApiKey, setLocalApiKey] = useState(apiKey);
  const [isKeySaved, setIsKeySaved] = useState(false);

  useEffect(() => {
    setLocalApiKey(apiKey);
  }, [apiKey]);

  const handleImportClick = () => {
    onImport();
  };

  const handleApiKeySave = () => {
    onSetApiKey(localApiKey);
    setIsKeySaved(true);
    setTimeout(() => setIsKeySaved(false), 2000);
  };

  return (
    <div className="fixed top-3 left-3 z-50 flex items-start gap-1">
      {/* Canvas Tabs */}
      <CanvasTabs
        canvases={canvases}
        canvasOrder={canvasOrder}
        activeCanvasId={activeCanvasId}
        onSwitchCanvas={onSwitchCanvas}
        onCloseCanvas={onCloseCanvas}
        onRenameCanvas={onRenameCanvas}
        onCreateCanvas={onNewCanvas}
        onDuplicateCanvas={onDuplicateCanvas}
      />

      {/* Main Toolbar */}
      <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-md border border-gray-200/50 dark:border-gray-700/50 px-1.5 py-1 flex items-center gap-1.5 shadow-lg">
          {/* Canvas Actions */}
          <div className="flex items-center gap-0.5">
            <button
              onClick={onExport}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              title="Export Mind Map"
            >
              <Download size={14} />
            </button>
            <button
              onClick={handleImportClick}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              title="Import Mind Map"
            >
              <Upload size={14} />
            </button>
          </div>

          <div className="w-px h-4 bg-gray-300 dark:bg-gray-700/50" />

          {/* View Controls */}
          <div className="flex items-center gap-0.5">
            <button
              onClick={onZoomOut}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              title="Zoom Out"
            >
              <ZoomOut size={14} />
            </button>
            <span className="text-xs font-mono text-gray-600 dark:text-gray-400 min-w-[3rem] text-center">
              {Math.round(viewport.zoom * 100)}%
            </span>
            <button
              onClick={onZoomIn}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              title="Zoom In"
            >
              <ZoomIn size={14} />
            </button>
          </div>

          <div className="w-px h-4 bg-gray-300 dark:bg-gray-700/50" />

          <button
            onClick={onResetView}
            className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Reset View"
          >
            <RotateCcw size={14} />
          </button>

          <div className="w-px h-4 bg-gray-300 dark:bg-gray-700/50" />

          <button
            onClick={onToggleTheme}
            className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title={`Switch to ${theme === 'dark' ? 'Light' : 'Dark'} Mode`}
          >
            {theme === 'dark' ? <Sun size={14} /> : <Moon size={14} />}
          </button>

          <div className="w-px h-4 bg-gray-300 dark:bg-gray-700/50" />

          {/* Model Selector */}
          <ModelSelector
            selectedModel={selectedModel}
            onModelChange={onSetSelectedModel}
            disabled={!apiKey}
          />

          <div className="w-px h-4 bg-gray-300 dark:bg-gray-700/50" />

          {/* API Key Input */}
          <div className="flex items-center gap-1.5">
            <KeyRound size={14} className="text-gray-600 dark:text-gray-400" />
            <input
              type="password"
              placeholder="API Key"
              className="bg-transparent text-xs w-32 focus:outline-none text-gray-700 dark:text-gray-300 placeholder-gray-400 dark:placeholder-gray-500"
              value={localApiKey}
              onChange={(e) => setLocalApiKey(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleApiKeySave()}
            />
            <button
              onClick={handleApiKeySave}
              className={`text-xs px-2 py-0.5 rounded transition-colors ${isKeySaved ? 'bg-green-500' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
              disabled={isKeySaved}
            >
              {isKeySaved ? 'Saved' : 'Save'}
            </button>
        </div>
      </div>
    </div>
  );
};