import React from 'react';
import { NodeData, ViewportState } from '../types';

interface MiniMapProps {
  nodes: Record<string, NodeData>;
  viewport: ViewportState;
  onViewportChange: (viewport: ViewportState) => void;
}

export const MiniMap: React.FC<MiniMapProps> = ({ nodes, viewport, onViewportChange }) => {
  const nodeArray = Object.values(nodes);
  
  if (nodeArray.length === 0) return null;

  // Calculate bounds of all nodes
  const bounds = nodeArray.reduce(
    (acc, node) => ({
      minX: Math.min(acc.minX, node.x),
      maxX: Math.max(acc.maxX, node.x + node.width),
      minY: Math.min(acc.minY, node.y),
      maxY: Math.max(acc.maxY, node.y + 200),
    }),
    { minX: Infinity, maxX: -Infinity, minY: Infinity, maxY: -Infinity }
  );

  const padding = 100;
  const mapWidth = 180;
  const mapHeight = 135;
  
  // Expand bounds to include some padding
  const expandedBounds = {
    minX: bounds.minX - padding,
    maxX: bounds.maxX + padding,
    minY: bounds.minY - padding,
    maxY: bounds.maxY + padding,
  };
  
  const worldWidth = expandedBounds.maxX - expandedBounds.minX;
  const worldHeight = expandedBounds.maxY - expandedBounds.minY;
  
  const scaleX = mapWidth / worldWidth;
  const scaleY = mapHeight / worldHeight;
  const scale = Math.min(scaleX, scaleY);

  // Calculate what area is currently visible in the main viewport
  const viewportWorldX = -viewport.x / viewport.zoom;
  const viewportWorldY = -viewport.y / viewport.zoom;
  const viewportWorldWidth = window.innerWidth / viewport.zoom;
  const viewportWorldHeight = window.innerHeight / viewport.zoom;

  // Convert viewport bounds to minimap coordinates
  const viewportMinimapX = (viewportWorldX - expandedBounds.minX) * scale;
  const viewportMinimapY = (viewportWorldY - expandedBounds.minY) * scale;
  const viewportMinimapWidth = viewportWorldWidth * scale;
  const viewportMinimapHeight = viewportWorldHeight * scale;

  const handleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickY = e.clientY - rect.top;
    
    // Convert minimap click to world coordinates
    const worldX = (clickX / scale) + expandedBounds.minX;
    const worldY = (clickY / scale) + expandedBounds.minY;
    
    // Center the viewport on the clicked point
    const newViewportX = -worldX * viewport.zoom + window.innerWidth / 2;
    const newViewportY = -worldY * viewport.zoom + window.innerHeight / 2;
    
    onViewportChange({
      ...viewport,
      x: newViewportX,
      y: newViewportY,
    });
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-md border border-gray-200/50 dark:border-gray-700/50 px-1.5 py-1 z-50 shadow-lg">
      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1 font-medium">Mini Map</div>
      <div
        className="relative bg-gray-100 dark:bg-gray-800 rounded cursor-pointer overflow-hidden"
        style={{ width: mapWidth, height: mapHeight }}
        onClick={handleClick}
      >
        <svg
          width={mapWidth}
          height={mapHeight}
          className="absolute inset-0"
        >
          {/* Render nodes */}
          {nodeArray.map((node) => {
            const nodeMinimapX = (node.x - expandedBounds.minX) * scale;
            const nodeMinimapY = (node.y - expandedBounds.minY) * scale;
            const nodeMinimapWidth = Math.max(2, node.width * scale);
            const nodeMinimapHeight = Math.max(2, 200 * scale);
            
            return (
              <rect
                key={node.id}
                x={nodeMinimapX}
                y={nodeMinimapY}
                width={nodeMinimapWidth}
                height={nodeMinimapHeight}
                fill={node.isSelected ? '#3b82f6' : node.hasQueried ? '#10b981' : '#6b7280'}
                opacity={0.8}
                rx={Math.max(1, 4 * scale)}
                className="transition-colors duration-200"
              />
            );
          })}
          
          {/* Render connections */}
          {nodeArray.map((node) =>
            node.childIds.map((childId) => {
              const child = nodes[childId];
              if (!child) return null;
              
              const parentX = (node.x + node.width - expandedBounds.minX) * scale;
              const parentY = (node.y + 30 - expandedBounds.minY) * scale;
              const childX = (child.x - expandedBounds.minX) * scale;
              const childY = (child.y + 30 - expandedBounds.minY) * scale;
              
              return (
                <line
                  key={`${node.id}-${childId}`}
                  x1={parentX}
                  y1={parentY}
                  x2={childX}
                  y2={childY}
                  stroke="#9ca3af"
                  strokeWidth="1"
                  opacity={0.6}
                />
              );
            })
          )}
          
          {/* Viewport indicator - shows what's currently visible */}
          <rect
            x={Math.max(0, Math.min(mapWidth, viewportMinimapX))}
            y={Math.max(0, Math.min(mapHeight, viewportMinimapY))}
            width={Math.max(0, Math.min(mapWidth - Math.max(0, viewportMinimapX), viewportMinimapWidth))}
            height={Math.max(0, Math.min(mapHeight - Math.max(0, viewportMinimapY), viewportMinimapHeight))}
            fill="none"
            stroke="#f59e0b"
            strokeWidth="2"
            opacity={0.9}
            rx="2"
            className="pointer-events-none"
          />
          
          {/* Viewport center indicator */}
          <circle
            cx={Math.max(2, Math.min(mapWidth - 2, viewportMinimapX + viewportMinimapWidth / 2))}
            cy={Math.max(2, Math.min(mapHeight - 2, viewportMinimapY + viewportMinimapHeight / 2))}
            r="2"
            fill="#f59e0b"
            opacity={0.8}
            className="pointer-events-none"
          />
        </svg>
        
        {/* Zoom level indicator */}
        <div className="absolute bottom-1 right-1 bg-black/20 dark:bg-white/20 text-white dark:text-gray-200 text-xs px-1 py-0.5 rounded backdrop-blur-sm">
          {Math.round(viewport.zoom * 100)}%
        </div>
      </div>
    </div>
  );
};