import React from 'react';
import { ExternalLink, Twitter } from 'lucide-react';

export const SocialLinks: React.FC = () => {
  return (
    <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-md border border-gray-200/50 dark:border-gray-700/50 px-1 py-0.5 flex items-center gap-0.5 shadow-lg">
      <a
        href="https://bagusfarisa.com"
        target="_blank"
        rel="noopener noreferrer"
        className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        title="Personal Website"
      >
        <ExternalLink size={14} />
      </a>
      <a
        href="https://twitter.com/bagusfarisa"
        target="_blank"
        rel="noopener noreferrer"
        className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        title="Twitter"
      >
        <Twitter size={14} />
      </a>
    </div>
  );
};
