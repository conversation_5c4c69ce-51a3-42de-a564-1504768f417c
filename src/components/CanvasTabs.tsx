import React, { useState, useRef, useEffect } from 'react';
import { X, Edit2, Check, Plus, Copy } from 'lucide-react';
import { CanvasData } from '../types';

interface CanvasTabsProps {
  canvases: Record<string, CanvasData>;
  canvasOrder: string[];
  activeCanvasId: string;
  onSwitchCanvas: (canvasId: string) => void;
  onCloseCanvas: (canvasId: string) => void;
  onRenameCanvas: (canvasId: string, newName: string) => void;
  onCreateCanvas: () => void;
  onDuplicateCanvas: (canvasId: string) => void;
}

export const CanvasTabs: React.FC<CanvasTabsProps> = ({
  canvases,
  canvasOrder,
  activeCanvasId,
  onSwitchCanvas,
  onCloseCanvas,
  onRenameCanvas,
  onCreateCanvas,
  onDuplicateCanvas,
}) => {
  const [editingCanvasId, setEditingCanvasId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const editInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (editingCanvasId && editInputRef.current) {
      editInputRef.current.focus();
      editInputRef.current.select();
    }
  }, [editingCanvasId]);

  const handleStartEdit = (canvasId: string, currentName: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingCanvasId(canvasId);
    setEditingName(currentName);
  };

  const handleFinishEdit = () => {
    if (editingCanvasId && editingName.trim()) {
      onRenameCanvas(editingCanvasId, editingName.trim());
    }
    setEditingCanvasId(null);
    setEditingName('');
  };

  const handleCancelEdit = () => {
    setEditingCanvasId(null);
    setEditingName('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleFinishEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const handleCloseCanvas = (canvasId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (canvasOrder.length > 1) {
      onCloseCanvas(canvasId);
    }
  };

  const handleDuplicateCanvas = (canvasId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onDuplicateCanvas(canvasId);
  };

  return (
    <div className="flex items-center gap-0.5 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-md border border-gray-200/50 dark:border-gray-700/50 px-1 py-0.5 shadow-lg">
      {/* Canvas Tabs */}
      <div className="flex items-center gap-0.5 max-w-[600px] overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
        {canvasOrder.map((canvasId) => {
          const canvas = canvases[canvasId];
          if (!canvas) return null;

          const isActive = canvasId === activeCanvasId;
          const isEditing = editingCanvasId === canvasId;

          return (
            <div
              key={canvasId}
              className={`
                group relative flex items-center gap-1 px-2 py-1 rounded cursor-pointer transition-all duration-200 min-w-0 max-w-[180px]
                ${isActive
                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100 border border-blue-200 dark:border-blue-700'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-700 dark:text-gray-300'
                }
              `}
              onClick={() => !isEditing && onSwitchCanvas(canvasId)}
            >
              {isEditing ? (
                <div className="flex items-center gap-1 min-w-0 flex-1">
                  <input
                    ref={editInputRef}
                    type="text"
                    value={editingName}
                    onChange={(e) => setEditingName(e.target.value)}
                    onKeyDown={handleKeyDown}
                    onBlur={handleFinishEdit}
                    className="bg-transparent border-none outline-none text-sm min-w-0 flex-1"
                    maxLength={50}
                  />
                  <button
                    onClick={handleFinishEdit}
                    className="p-0.5 hover:bg-green-100 dark:hover:bg-green-900/50 rounded text-green-600 dark:text-green-400"
                  >
                    <Check size={12} />
                  </button>
                </div>
              ) : (
                <>
                  <span className="text-xs font-medium truncate min-w-0 flex-1">
                    {canvas.name}
                  </span>
                  
                  {/* Action buttons - only show on hover or active */}
                  <div className={`flex items-center gap-0.5 ${isActive || 'group-hover:opacity-100 opacity-0'} transition-opacity`}>
                    <button
                      onClick={(e) => handleStartEdit(canvasId, canvas.name, e)}
                      className="p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                      title="Rename canvas"
                    >
                      <Edit2 size={12} />
                    </button>
                    
                    <button
                      onClick={(e) => handleDuplicateCanvas(canvasId, e)}
                      className="p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                      title="Duplicate canvas"
                    >
                      <Copy size={12} />
                    </button>
                    
                    {canvasOrder.length > 1 && (
                      <button
                        onClick={(e) => handleCloseCanvas(canvasId, e)}
                        className="p-0.5 hover:bg-red-100 dark:hover:bg-red-900/50 rounded text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                        title="Close canvas"
                      >
                        <X size={12} />
                      </button>
                    )}
                  </div>
                </>
              )}
            </div>
          );
        })}
      </div>

      {/* Add New Canvas Button */}
      <button
        onClick={onCreateCanvas}
        className="flex items-center gap-1 px-1.5 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        title="Create new canvas"
      >
        <Plus size={12} />
        <span className="text-xs font-medium">New</span>
      </button>
    </div>
  );
};
