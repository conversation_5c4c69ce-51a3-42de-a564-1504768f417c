import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { CanvasData, CanvasManagerState, MindMapData, ViewportState, NodeData } from '../types';
import { DEFAULT_MODEL } from '../config/models';

const CANVAS_MANAGER_STORAGE_KEY = 'canvas-manager-data';
const LEGACY_STORAGE_KEY = 'mindmap-data';

const createInitialNode = (x: number = 400, y: number = 300, searchGrounding: boolean = false): NodeData => ({
  id: uuidv4(),
  x,
  y,
  title: 'Main Topic',
  query: undefined,
  response: undefined,
  sources: undefined,
  messages: [],
  isExpanded: true,
  childIds: [],
  isSelected: false,
  width: 450,
  height: 200,
  searchGrounding,
  hasQueried: false,
});

const createInitialMindMapData = (): MindMapData => {
  const rootNode = createInitialNode();
  return {
    nodes: { [rootNode.id]: rootNode },
    rootNodeId: rootNode.id,
    searchGrounding: false,
    selectedModel: DEFAULT_MODEL.id,
    version: 1,
    lastModified: Date.now(),
  };
};

const createInitialCanvas = (name?: string): CanvasData => {
  const id = uuidv4();
  const now = Date.now();
  return {
    id,
    name: name || `Canvas ${new Date().toLocaleDateString()}`,
    mindMapData: createInitialMindMapData(),
    viewport: { x: 0, y: 0, zoom: 1 },
    createdAt: now,
    lastModified: now,
  };
};

const migrateLegacyData = (): CanvasManagerState => {
  // Check for legacy single-canvas data
  const legacyData = localStorage.getItem(LEGACY_STORAGE_KEY);
  if (legacyData) {
    try {
      const mindMapData: MindMapData = JSON.parse(legacyData);
      const canvas = createInitialCanvas('Imported Canvas');
      canvas.mindMapData = mindMapData;
      
      // Remove legacy data after migration
      localStorage.removeItem(LEGACY_STORAGE_KEY);
      
      return {
        canvases: { [canvas.id]: canvas },
        activeCanvasId: canvas.id,
        canvasOrder: [canvas.id],
      };
    } catch (error) {
      console.warn('Failed to migrate legacy data:', error);
    }
  }
  
  // Create initial state with one canvas
  const initialCanvas = createInitialCanvas();
  return {
    canvases: { [initialCanvas.id]: initialCanvas },
    activeCanvasId: initialCanvas.id,
    canvasOrder: [initialCanvas.id],
  };
};

export const useCanvasManager = () => {
  const [state, setState] = useState<CanvasManagerState>(() => {
    const saved = localStorage.getItem(CANVAS_MANAGER_STORAGE_KEY);
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (error) {
        console.warn('Failed to parse canvas manager data:', error);
      }
    }
    return migrateLegacyData();
  });

  // Auto-save to localStorage
  useEffect(() => {
    localStorage.setItem(CANVAS_MANAGER_STORAGE_KEY, JSON.stringify(state));
  }, [state]);

  const getActiveCanvas = useCallback((): CanvasData | null => {
    return state.canvases[state.activeCanvasId] || null;
  }, [state.canvases, state.activeCanvasId]);

  const createCanvas = useCallback((name?: string) => {
    const newCanvas = createInitialCanvas(name);
    setState(prev => ({
      ...prev,
      canvases: {
        ...prev.canvases,
        [newCanvas.id]: newCanvas,
      },
      activeCanvasId: newCanvas.id,
      canvasOrder: [...prev.canvasOrder, newCanvas.id],
    }));
    return newCanvas.id;
  }, []);

  const switchToCanvas = useCallback((canvasId: string) => {
    if (state.canvases[canvasId]) {
      setState(prev => ({
        ...prev,
        activeCanvasId: canvasId,
      }));
    }
  }, [state.canvases]);

  const updateCanvas = useCallback((canvasId: string, updates: Partial<Pick<CanvasData, 'mindMapData' | 'viewport' | 'name'>>) => {
    setState(prev => {
      const canvas = prev.canvases[canvasId];
      if (!canvas) return prev;

      return {
        ...prev,
        canvases: {
          ...prev.canvases,
          [canvasId]: {
            ...canvas,
            ...updates,
            lastModified: Date.now(),
          },
        },
      };
    });
  }, []);

  const renameCanvas = useCallback((canvasId: string, newName: string) => {
    updateCanvas(canvasId, { name: newName.trim() || 'Untitled Canvas' });
  }, [updateCanvas]);

  const deleteCanvas = useCallback((canvasId: string) => {
    setState(prev => {
      // Don't delete if it's the only canvas
      if (prev.canvasOrder.length <= 1) {
        return prev;
      }

      const newCanvases = { ...prev.canvases };
      delete newCanvases[canvasId];

      const newOrder = prev.canvasOrder.filter(id => id !== canvasId);
      
      // If we're deleting the active canvas, switch to another one
      let newActiveId = prev.activeCanvasId;
      if (canvasId === prev.activeCanvasId) {
        const currentIndex = prev.canvasOrder.indexOf(canvasId);
        // Try to switch to the next canvas, or previous if it was the last one
        const nextIndex = currentIndex < newOrder.length ? currentIndex : currentIndex - 1;
        newActiveId = newOrder[nextIndex];
      }

      return {
        canvases: newCanvases,
        activeCanvasId: newActiveId,
        canvasOrder: newOrder,
      };
    });
  }, []);

  const duplicateCanvas = useCallback((canvasId: string) => {
    const canvas = state.canvases[canvasId];
    if (!canvas) return null;

    const newCanvas: CanvasData = {
      ...canvas,
      id: uuidv4(),
      name: `${canvas.name} (Copy)`,
      createdAt: Date.now(),
      lastModified: Date.now(),
      // Deep copy the mind map data
      mindMapData: {
        ...canvas.mindMapData,
        nodes: { ...canvas.mindMapData.nodes },
        lastModified: Date.now(),
      },
    };

    setState(prev => ({
      ...prev,
      canvases: {
        ...prev.canvases,
        [newCanvas.id]: newCanvas,
      },
      activeCanvasId: newCanvas.id,
      canvasOrder: [...prev.canvasOrder, newCanvas.id],
    }));

    return newCanvas.id;
  }, [state.canvases]);

  return {
    state,
    activeCanvas: getActiveCanvas(),
    createCanvas,
    switchToCanvas,
    updateCanvas,
    renameCanvas,
    deleteCanvas,
    duplicateCanvas,
  };
};
