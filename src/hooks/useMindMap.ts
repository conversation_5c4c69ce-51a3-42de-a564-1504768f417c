import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { MindMapData, NodeData, ChatMessage, ViewportState } from '../types';
import { DEFAULT_MODEL } from '../config/models';
import { useCanvasManager } from './useCanvasManager';

const API_KEY_STORAGE_KEY = 'gemini-api-key';
const SELECTED_MODEL_STORAGE_KEY = 'gemini-selected-model';

export const useMindMap = () => {
  const canvasManager = useCanvasManager();
  const activeCanvas = canvasManager.activeCanvas;

  const [apiKey, setApiKey] = useState<string>(() => {
    return localStorage.getItem(API_KEY_STORAGE_KEY) || '';
  });

  const [selectedModel, setSelectedModel] = useState<string>(() => {
    return localStorage.getItem(SELECTED_MODEL_STORAGE_KEY) || DEFAULT_MODEL.id;
  });

  // Get current data and viewport from active canvas
  const data = activeCanvas?.mindMapData || { nodes: {}, rootNodeId: '', searchGrounding: false, version: 1, lastModified: Date.now() };
  const viewport = activeCanvas?.viewport || { x: 0, y: 0, zoom: 1 };

  // Update canvas data when mind map data changes
  const setData = useCallback((newData: MindMapData | ((prev: MindMapData) => MindMapData)) => {
    if (!activeCanvas) return;

    const updatedData = typeof newData === 'function' ? newData(activeCanvas.mindMapData) : newData;
    canvasManager.updateCanvas(activeCanvas.id, { mindMapData: updatedData });
  }, [activeCanvas, canvasManager]);

  // Update canvas viewport
  const setViewport = useCallback((newViewport: ViewportState | ((prev: ViewportState) => ViewportState)) => {
    if (!activeCanvas) return;

    const updatedViewport = typeof newViewport === 'function' ? newViewport(activeCanvas.viewport) : newViewport;
    canvasManager.updateCanvas(activeCanvas.id, { viewport: updatedViewport });
  }, [activeCanvas, canvasManager]);

  useEffect(() => {
    if (apiKey) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY);
    }
  }, [apiKey]);

  useEffect(() => {
    localStorage.setItem(SELECTED_MODEL_STORAGE_KEY, selectedModel);
  }, [selectedModel]);

  const updateNode = useCallback((nodeId: string, updates: Partial<NodeData>) => {
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: { ...prev.nodes[nodeId], ...updates },
      },
    }));
  }, []);

  const addMessage = useCallback((nodeId: string, message: ChatMessage) => {
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          messages: [...prev.nodes[nodeId].messages, message],
        },
      },
    }));
  }, []);

  const setNodeQuery = useCallback((nodeId: string, query: string, response: string, sources?: Array<{title: string; uri: string}>) => {
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          query,
          response,
          sources,
          hasQueried: true,
        },
      },
    }));
  }, []);

  const clearNodeQuery = useCallback((nodeId: string) => {
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          query: undefined,
          response: undefined,
          sources: undefined,
          hasQueried: false,
        },
      },
    }));
  }, []);

  const createChildNode = useCallback((parentId: string) => {
    const parent = data.nodes[parentId];
    if (!parent) return;

    const newNode: NodeData = {
      id: uuidv4(),
      x: parent.x + parent.width + 50, // Position to the right of the parent with a margin
      y: parent.y + parent.childIds.length * 250,
      title: 'New Topic',
      query: undefined,
      response: undefined,
      sources: undefined,
      messages: [],
      isExpanded: true,
      parentId,
      childIds: [],
      isSelected: false,
      width: 450,
      height: 200,
      searchGrounding: data.searchGrounding, // Use global default for new nodes
      hasQueried: false,
    };

    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [newNode.id]: newNode,
        [parentId]: {
          ...prev.nodes[parentId],
          childIds: [...prev.nodes[parentId].childIds, newNode.id],
        },
      },
    }));

    return newNode.id;
  }, [data.nodes, data.searchGrounding]);

  const deleteNode = useCallback((nodeId: string) => {
    const node = data.nodes[nodeId];
    if (!node || nodeId === data.rootNodeId) return;

    setData(prev => {
      const newNodes = { ...prev.nodes };
      
      // Remove from parent's children
      if (node.parentId) {
        const parent = newNodes[node.parentId];
        if (parent) {
          newNodes[node.parentId] = {
            ...parent,
            childIds: parent.childIds.filter(id => id !== nodeId),
          };
        }
      }

      // Delete node and all its children recursively
      const deleteRecursively = (id: string) => {
        const nodeToDelete = newNodes[id];
        if (nodeToDelete) {
          nodeToDelete.childIds.forEach(deleteRecursively);
          delete newNodes[id];
        }
      };

      deleteRecursively(nodeId);

      return { ...prev, nodes: newNodes };
    });
  }, [data.nodes, data.rootNodeId]);

  const toggleSearchGrounding = useCallback(() => {
    setData(prev => ({ ...prev, searchGrounding: !prev.searchGrounding }));
  }, []);

  const toggleNodeSearchGrounding = useCallback((nodeId: string) => {
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          searchGrounding: !prev.nodes[nodeId].searchGrounding,
        },
      },
    }));
  }, []);

  const clearNodeChat = useCallback((nodeId: string) => {
    updateNode(nodeId, { messages: [] });
  }, [updateNode]);

  const selectNode = useCallback((nodeId: string) => {
    setData(prev => ({
      ...prev,
      nodes: Object.fromEntries(
        Object.entries(prev.nodes).map(([id, node]) => [
          id,
          { ...node, isSelected: id === nodeId },
        ])
      ),
    }));
  }, []);

  const newCanvas = useCallback(() => {
    canvasManager.createCanvas();
  }, [canvasManager]);

  const exportData = useCallback(() => {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mindmap-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [data]);

  const importData = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        // Validate the imported data structure
        if (imported.nodes && imported.rootNodeId && imported.nodes[imported.rootNodeId]) {
          setData(imported);
          setViewport({ x: 0, y: 0, zoom: 1 }); // Reset viewport when importing
        } else {
          console.error('Invalid mind map file format');
          alert('Invalid mind map file format. Please select a valid mind map export file.');
        }
      } catch (error) {
        console.error('Failed to import data:', error);
        alert('Failed to import mind map. Please check the file format.');
      }
    };
    reader.readAsText(file);
  }, [setData, setViewport]);

  return {
    data,
    viewport,
    setViewport,
    updateNode,
    addMessage,
    setNodeQuery,
    clearNodeQuery,
    createChildNode,
    deleteNode,
    toggleSearchGrounding,
    toggleNodeSearchGrounding,
    clearNodeChat,
    selectNode,
    newCanvas,
    exportData,
    importData,
    apiKey,
    setApiKey,
    selectedModel,
    setSelectedModel,
    // Canvas management
    canvasManager,
  };
};